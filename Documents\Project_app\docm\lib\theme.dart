import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

var appTheme = ThemeData(
  fontFamily: GoogleFonts.roboto().fontFamily,
  brightness: Brightness.dark,
  colorScheme: ColorScheme.fromSwatch(
    brightness: Brightness.dark,
    primarySwatch: Colors.indigo,
    accentColor: Colors.pinkAccent,
  ),
  textTheme: const TextTheme(
    bodyLarge: TextStyle(fontSize: 18, fontFamily: 'Roboto'),
    bodyMedium: TextStyle(fontSize: 16, fontFamily: 'Roboto'),
    labelLarge: TextStyle(letterSpacing: 1.5, fontWeight: FontWeight.bold),
  ),
);
