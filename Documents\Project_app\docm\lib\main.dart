import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:docm/routes.dart';
import 'package:docm/theme.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final Future<FirebaseApp> _initialization = Firebase.initializeApp();

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _initialization,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text('Error');
        }

        if (snapshot.connectionState == ConnectionState.done) {
          return MaterialApp(routes: appRoutes, theme: appTheme);
        }

        return Text('loading');
      },
    );
  }
}
