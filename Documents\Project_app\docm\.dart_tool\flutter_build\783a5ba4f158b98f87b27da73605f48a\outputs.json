["C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/congrats.gif", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/logo.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/user.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/angular.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/cloud-functions.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/default-cover.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/firebase.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/firestore.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/flutter-layout.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/flutter.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/js.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/rxjs.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/covers/ts.png", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\Users\\<USER>\\Documents\\Project_app\\docm\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]